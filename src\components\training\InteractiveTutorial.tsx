import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
  SkipForward,
  BookOpen,
  Target,
  Users,
  Zap,
  Download,
  Settings,
  Eye,
  MousePointer,
  Keyboard,
  Lightbulb,
  CheckCircle,
  Star,
  Rocket
} from 'lucide-react';

interface TutorialStep {
  id: string;
  title: string;
  description: string;
  content: React.ReactNode;
  target?: string;
  action?: 'click' | 'drag' | 'type' | 'observe';
  duration?: number;
  tips?: string[];
}

interface InteractiveTutorialProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onComplete?: () => void;
}

export function InteractiveTutorial({ open, onOpenChange, onComplete }: InteractiveTutorialProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [completedSteps, setCompletedSteps] = useState<string[]>([]);

  const tutorialSteps: TutorialStep[] = [
    {
      id: 'welcome',
      title: 'Bem-vindo ao Sistema de Micro Ciclos',
      description: 'Aprenda a criar treinos interativos e visuais',
      content: (
        <div className="space-y-4">
          <div className="text-center">
            <Rocket className="h-16 w-16 text-primary mx-auto mb-4" />
            <h3 className="text-lg font-semibold">Sistema Revolucionário de Treinamento</h3>
            <p className="text-muted-foreground">
              Crie drills visuais, interativos e profissionais com facilidade
            </p>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <Target className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                <h4 className="font-medium">Campo Visual</h4>
                <p className="text-xs text-muted-foreground">
                  Campo realista com elementos arrastáveis
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4 text-center">
                <Zap className="h-8 w-8 text-green-500 mx-auto mb-2" />
                <h4 className="font-medium">Animações</h4>
                <p className="text-xs text-muted-foreground">
                  Trajetórias e movimentos animados
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      ),
      tips: [
        'Use este sistema para criar treinos profissionais',
        'Todos os elementos são interativos e personalizáveis',
        'Exporte seus drills em múltiplos formatos'
      ]
    },
    {
      id: 'field-basics',
      title: 'Conhecendo o Campo',
      description: 'Explore o campo de treinamento e suas funcionalidades',
      content: (
        <div className="space-y-4">
          <div className="bg-green-50 p-4 rounded-lg border">
            <h4 className="font-medium mb-2">Campo de Treinamento</h4>
            <p className="text-sm text-muted-foreground mb-3">
              O campo é desenhado com proporções reais e inclui todas as marcações oficiais.
            </p>
            
            <div className="grid grid-cols-2 gap-3 text-xs">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-600 rounded"></div>
                <span>Linhas do campo</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-purple-600 rounded"></div>
                <span>Gols</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-blue-600 rounded"></div>
                <span>Áreas penais</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-gray-400 rounded"></div>
                <span>Grade opcional</span>
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <h4 className="font-medium">Controles de Visualização</h4>
            <div className="grid grid-cols-3 gap-2 text-xs">
              <Badge variant="outline">Zoom: Mouse wheel</Badge>
              <Badge variant="outline">Pan: Arrastar campo</Badge>
              <Badge variant="outline">Reset: Duplo clique</Badge>
            </div>
          </div>
        </div>
      ),
      action: 'observe',
      tips: [
        'Use o zoom para ver detalhes ou visão geral',
        'A grade ajuda no posicionamento preciso',
        'Todas as medidas são proporcionais ao campo real'
      ]
    },
    {
      id: 'adding-elements',
      title: 'Adicionando Elementos',
      description: 'Aprenda a adicionar cones, bolas, jogadores e outros elementos',
      content: (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardContent className="p-3">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-4 h-4 bg-orange-500 rounded"></div>
                  <span className="font-medium text-sm">Cones</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Múltiplas cores para diferentes exercícios
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-3">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-4 h-4 bg-white border-2 border-black rounded-full"></div>
                  <span className="font-medium text-sm">Bolas</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Tamanhos variados para diferentes idades
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-3">
                <div className="flex items-center gap-2 mb-2">
                  <Users className="w-4 h-4 text-blue-500" />
                  <span className="font-medium text-sm">Jogadores</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Conectados ao banco de dados do clube
                </p>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-3">
                <div className="flex items-center gap-2 mb-2">
                  <div className="w-4 h-4 bg-purple-500 rounded"></div>
                  <span className="font-medium text-sm">Gols</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  Pequenos, médios e grandes
                </p>
              </CardContent>
            </Card>
          </div>
          
          <div className="bg-blue-50 p-3 rounded-lg">
            <h4 className="font-medium text-sm mb-1">Como adicionar:</h4>
            <ol className="text-xs text-muted-foreground space-y-1">
              <li>1. Arraste da barra de ferramentas para o campo</li>
              <li>2. Ou clique no elemento e depois no campo</li>
              <li>3. Personalize as propriedades</li>
            </ol>
          </div>
        </div>
      ),
      action: 'drag',
      tips: [
        'Arraste elementos diretamente para posicionamento rápido',
        'Use cores diferentes para organizar exercícios',
        'Jogadores mostram foto e número da camisa'
      ]
    },
    {
      id: 'creating-sequences',
      title: 'Criando Sequências',
      description: 'Monte sequências de exercícios com múltiplos passos',
      content: (
        <div className="space-y-4">
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Sistema de Passos</h4>
            <p className="text-sm text-muted-foreground mb-3">
              Cada drill pode ter múltiplos passos com configurações diferentes.
            </p>
            
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm">
                <Badge variant="outline">1</Badge>
                <span>Configuração inicial</span>
                <Badge variant="secondary">2min</Badge>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Badge variant="outline">2</Badge>
                <span>Aquecimento</span>
                <Badge variant="secondary">5min</Badge>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Badge variant="outline">3</Badge>
                <span>Exercício principal</span>
                <Badge variant="secondary">15min</Badge>
              </div>
              <div className="flex items-center gap-2 text-sm">
                <Badge variant="outline">4</Badge>
                <span>Finalização</span>
                <Badge variant="secondary">3min</Badge>
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center">
              <Play className="h-8 w-8 text-green-500 mx-auto mb-1" />
              <p className="text-xs font-medium">Reprodução</p>
              <p className="text-xs text-muted-foreground">Veja a sequência animada</p>
            </div>
            <div className="text-center">
              <Settings className="h-8 w-8 text-blue-500 mx-auto mb-1" />
              <p className="text-xs font-medium">Configuração</p>
              <p className="text-xs text-muted-foreground">Ajuste tempo e elementos</p>
            </div>
          </div>
        </div>
      ),
      action: 'click',
      tips: [
        'Cada passo pode ter elementos diferentes',
        'Configure durações específicas para cada fase',
        'Use a reprodução para visualizar o fluxo completo'
      ]
    },
    {
      id: 'animations-trajectories',
      title: 'Animações e Trajetórias',
      description: 'Crie movimentos e trajetórias para elementos',
      content: (
        <div className="space-y-4">
          <div className="bg-gradient-to-r from-green-50 to-blue-50 p-4 rounded-lg">
            <h4 className="font-medium mb-2">Sistema de Trajetórias</h4>
            <p className="text-sm text-muted-foreground mb-3">
              Grave movimentos em tempo real ou desenhe trajetórias personalizadas.
            </p>
            
            <div className="grid grid-cols-2 gap-3">
              <div className="space-y-2">
                <h5 className="text-xs font-medium">Tipos de Movimento:</h5>
                <div className="space-y-1 text-xs">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-1 bg-red-500"></div>
                    <span>Sprint</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-1 bg-blue-500"></div>
                    <span>Corrida</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-1 bg-green-500"></div>
                    <span>Caminhada</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-1 bg-purple-500 border-dashed"></div>
                    <span>Passe</span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <h5 className="text-xs font-medium">Controles:</h5>
                <div className="space-y-1 text-xs">
                  <Badge variant="outline">Play/Pause: Espaço</Badge>
                  <Badge variant="outline">Frame: ← →</Badge>
                  <Badge variant="outline">Velocidade: 0.5x - 5x</Badge>
                </div>
              </div>
            </div>
          </div>
          
          <div className="text-center">
            <Eye className="h-12 w-12 text-purple-500 mx-auto mb-2" />
            <p className="text-sm font-medium">Visualização Avançada</p>
            <p className="text-xs text-muted-foreground">
              Veja movimentos, velocidades e interações em tempo real
            </p>
          </div>
        </div>
      ),
      action: 'observe',
      tips: [
        'Grave movimentos arrastando elementos durante a reprodução',
        'Use diferentes cores para distinguir tipos de movimento',
        'Exporte animações como vídeo ou GIF'
      ]
    },
    {
      id: 'analysis-export',
      title: 'Análise e Exportação',
      description: 'Analise drills e exporte em múltiplos formatos',
      content: (
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardContent className="p-3">
                <h4 className="font-medium text-sm mb-2">Análise Tática</h4>
                <div className="space-y-1 text-xs">
                  <div>• Intensidade do exercício</div>
                  <div>• Cobertura espacial</div>
                  <div>• Interação entre jogadores</div>
                  <div>• Demanda física</div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-3">
                <h4 className="font-medium text-sm mb-2">Formatos de Exportação</h4>
                <div className="space-y-1 text-xs">
                  <div>• PDF com instruções</div>
                  <div>• Imagens PNG/SVG</div>
                  <div>• Vídeos MP4</div>
                  <div>• Apresentações PowerPoint</div>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="bg-yellow-50 p-3 rounded-lg border border-yellow-200">
            <div className="flex items-start gap-2">
              <Lightbulb className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-sm">Recomendações Inteligentes</h4>
                <p className="text-xs text-muted-foreground">
                  O sistema analisa seu drill e sugere melhorias automáticas
                </p>
              </div>
            </div>
          </div>
          
          <div className="text-center">
            <Download className="h-12 w-12 text-green-500 mx-auto mb-2" />
            <p className="text-sm font-medium">Compartilhamento Profissional</p>
            <p className="text-xs text-muted-foreground">
              Exporte e compartilhe drills com qualidade profissional
            </p>
          </div>
        </div>
      ),
      action: 'observe',
      tips: [
        'Use a análise para otimizar seus treinos',
        'Exporte em PDF para impressão ou apresentação',
        'Compartilhe vídeos nas redes sociais'
      ]
    },
    {
      id: 'completion',
      title: 'Parabéns! 🎉',
      description: 'Você completou o tutorial do sistema de micro ciclos',
      content: (
        <div className="space-y-4 text-center">
          <div>
            <Star className="h-16 w-16 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold">Tutorial Concluído!</h3>
            <p className="text-muted-foreground">
              Agora você está pronto para criar treinos incríveis
            </p>
          </div>
          
          <div className="grid grid-cols-3 gap-3">
            <div className="text-center">
              <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-1" />
              <p className="text-xs font-medium">Campo Visual</p>
            </div>
            <div className="text-center">
              <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-1" />
              <p className="text-xs font-medium">Elementos</p>
            </div>
            <div className="text-center">
              <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-1" />
              <p className="text-xs font-medium">Animações</p>
            </div>
          </div>
          
          <div className="bg-green-50 p-4 rounded-lg border border-green-200">
            <h4 className="font-medium text-sm mb-2">Próximos Passos:</h4>
            <div className="space-y-1 text-xs text-left">
              <div>• Crie seu primeiro drill personalizado</div>
              <div>• Explore a biblioteca de templates</div>
              <div>• Experimente diferentes tipos de exercícios</div>
              <div>• Compartilhe com sua equipe técnica</div>
            </div>
          </div>
        </div>
      ),
      tips: [
        'Pratique criando diferentes tipos de drills',
        'Use templates como ponto de partida',
        'Explore todas as funcionalidades avançadas'
      ]
    }
  ];

  const currentTutorialStep = tutorialSteps[currentStep];
  const progress = ((currentStep + 1) / tutorialSteps.length) * 100;

  const handleNext = () => {
    if (currentStep < tutorialSteps.length - 1) {
      setCompletedSteps(prev => [...prev, currentTutorialStep.id]);
      setCurrentStep(prev => prev + 1);
    } else {
      onComplete?.();
      onOpenChange(false);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleSkip = () => {
    onComplete?.();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            Tutorial Interativo - Sistema de Micro Ciclos
          </DialogTitle>
          <DialogDescription>
            Passo {currentStep + 1} de {tutorialSteps.length}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 flex-1">
          {/* Progress */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{currentTutorialStep.title}</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Content */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">{currentTutorialStep.title}</CardTitle>
              <CardDescription>{currentTutorialStep.description}</CardDescription>
            </CardHeader>
            <CardContent>
              {currentTutorialStep.content}
            </CardContent>
          </Card>

          {/* Tips */}
          {currentTutorialStep.tips && (
            <Card>
              <CardContent className="p-4">
                <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                  <Lightbulb className="h-4 w-4 text-yellow-500" />
                  Dicas Importantes
                </h4>
                <ul className="space-y-1">
                  {currentTutorialStep.tips.map((tip, index) => (
                    <li key={index} className="text-xs text-muted-foreground flex items-start gap-2">
                      <span className="text-primary">•</span>
                      <span>{tip}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Navigation */}
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleSkip}>
              Pular Tutorial
            </Button>
            {currentStep > 0 && (
              <Button variant="outline" onClick={handlePrevious}>
                <ChevronLeft className="h-4 w-4 mr-2" />
                Anterior
              </Button>
            )}
          </div>
          
          <Button onClick={handleNext}>
            {currentStep === tutorialSteps.length - 1 ? (
              <>
                <CheckCircle className="h-4 w-4 mr-2" />
                Finalizar
              </>
            ) : (
              <>
                Próximo
                <ChevronRight className="h-4 w-4 ml-2" />
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
