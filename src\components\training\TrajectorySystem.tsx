import React, { useState, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Route,
  ArrowRight,
  Zap,
  RotateCcw,
  Play,
  Pause,
  Settings,
  Trash2,
  Copy,
  Edit,
  Target,
  Timer,
  TrendingUp,
  Activity
} from 'lucide-react';

interface TrajectoryPoint {
  x: number;
  y: number;
  timestamp: number;
  speed?: number;
  action?: 'run' | 'walk' | 'sprint' | 'stop' | 'pass' | 'shoot' | 'dribble';
}

interface Trajectory {
  id: string;
  elementId: string;
  points: TrajectoryPoint[];
  type: 'player' | 'ball' | 'movement';
  style: {
    color: string;
    width: number;
    dashArray?: string;
    showArrows: boolean;
    showSpeed: boolean;
  };
  duration: number;
  name: string;
}

interface TrajectorySystemProps {
  trajectories: Trajectory[];
  onTrajectoriesChange: (trajectories: Trajectory[]) => void;
  selectedElementId?: string;
  isRecording: boolean;
  onRecordingChange: (recording: boolean) => void;
}

export function TrajectorySystem({
  trajectories,
  onTrajectoriesChange,
  selectedElementId,
  isRecording,
  onRecordingChange
}: TrajectorySystemProps) {
  const [selectedTrajectory, setSelectedTrajectory] = useState<string | null>(null);
  const [trajectoryStyle, setTrajectoryStyle] = useState({
    color: '#3b82f6',
    width: 2,
    showArrows: true,
    showSpeed: false,
    dashArray: ''
  });
  const [movementType, setMovementType] = useState<'run' | 'walk' | 'sprint'>('run');
  const [autoSpeed, setAutoSpeed] = useState(true);

  const trajectory = selectedTrajectory 
    ? trajectories.find(t => t.id === selectedTrajectory)
    : null;

  const handleCreateTrajectory = useCallback(() => {
    if (!selectedElementId) return;

    const newTrajectory: Trajectory = {
      id: `trajectory_${Date.now()}`,
      elementId: selectedElementId,
      points: [],
      type: 'player',
      style: { ...trajectoryStyle },
      duration: 0,
      name: `Trajetória ${trajectories.length + 1}`
    };

    onTrajectoriesChange([...trajectories, newTrajectory]);
    setSelectedTrajectory(newTrajectory.id);
  }, [selectedElementId, trajectories, trajectoryStyle, onTrajectoriesChange]);

  const handleDeleteTrajectory = useCallback((trajectoryId: string) => {
    onTrajectoriesChange(trajectories.filter(t => t.id !== trajectoryId));
    if (selectedTrajectory === trajectoryId) {
      setSelectedTrajectory(null);
    }
  }, [trajectories, selectedTrajectory, onTrajectoriesChange]);

  const handleDuplicateTrajectory = useCallback((trajectoryId: string) => {
    const original = trajectories.find(t => t.id === trajectoryId);
    if (!original) return;

    const duplicate: Trajectory = {
      ...original,
      id: `trajectory_${Date.now()}`,
      name: `${original.name} (Cópia)`,
      points: original.points.map(p => ({ ...p }))
    };

    onTrajectoriesChange([...trajectories, duplicate]);
  }, [trajectories, onTrajectoriesChange]);

  const handleUpdateTrajectory = useCallback((trajectoryId: string, updates: Partial<Trajectory>) => {
    onTrajectoriesChange(
      trajectories.map(t => 
        t.id === trajectoryId ? { ...t, ...updates } : t
      )
    );
  }, [trajectories, onTrajectoriesChange]);

  const calculateTrajectoryStats = (trajectory: Trajectory) => {
    if (trajectory.points.length < 2) return null;

    const totalDistance = trajectory.points.reduce((acc, point, index) => {
      if (index === 0) return 0;
      const prev = trajectory.points[index - 1];
      const distance = Math.sqrt(
        Math.pow(point.x - prev.x, 2) + Math.pow(point.y - prev.y, 2)
      );
      return acc + distance;
    }, 0);

    const totalTime = trajectory.duration;
    const avgSpeed = totalTime > 0 ? totalDistance / totalTime : 0;
    const maxSpeed = Math.max(...trajectory.points.map(p => p.speed || 0));

    return {
      distance: totalDistance,
      duration: totalTime,
      avgSpeed,
      maxSpeed,
      points: trajectory.points.length
    };
  };

  const getMovementIcon = (action?: string) => {
    switch (action) {
      case 'sprint':
        return <Zap className="h-3 w-3" />;
      case 'run':
        return <TrendingUp className="h-3 w-3" />;
      case 'walk':
        return <Activity className="h-3 w-3" />;
      case 'pass':
        return <Target className="h-3 w-3" />;
      default:
        return <Route className="h-3 w-3" />;
    }
  };

  const getMovementColor = (action?: string) => {
    switch (action) {
      case 'sprint':
        return 'bg-red-100 text-red-800';
      case 'run':
        return 'bg-blue-100 text-blue-800';
      case 'walk':
        return 'bg-green-100 text-green-800';
      case 'pass':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Route className="h-4 w-4" />
          Sistema de Trajetórias
        </CardTitle>
        <CardDescription className="text-xs">
          Crie e gerencie movimentos e trajetórias dos elementos
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Controles de gravação */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium">Gravação</Label>
            <Button
              variant={isRecording ? "destructive" : "default"}
              size="sm"
              onClick={() => onRecordingChange(!isRecording)}
              disabled={!selectedElementId}
            >
              {isRecording ? (
                <>
                  <Pause className="h-3 w-3 mr-1" />
                  Parar
                </>
              ) : (
                <>
                  <Play className="h-3 w-3 mr-1" />
                  Gravar
                </>
              )}
            </Button>
          </div>
          
          {isRecording && (
            <div className="p-2 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-xs text-red-700">
                🔴 Gravando movimento do elemento selecionado...
              </p>
            </div>
          )}
        </div>

        {/* Criar nova trajetória */}
        <div className="space-y-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleCreateTrajectory}
            disabled={!selectedElementId}
            className="w-full"
          >
            <Route className="h-3 w-3 mr-1" />
            Nova Trajetória
          </Button>
          
          {!selectedElementId && (
            <p className="text-xs text-muted-foreground">
              Selecione um elemento no campo para criar trajetórias
            </p>
          )}
        </div>

        {/* Lista de trajetórias */}
        <div className="space-y-2">
          <Label className="text-xs font-medium">
            Trajetórias ({trajectories.length})
          </Label>
          
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {trajectories.map(traj => {
              const stats = calculateTrajectoryStats(traj);
              return (
                <div
                  key={traj.id}
                  className={`p-2 rounded border cursor-pointer ${
                    selectedTrajectory === traj.id 
                      ? 'border-primary bg-primary/5' 
                      : 'border-muted hover:border-primary/50'
                  }`}
                  onClick={() => setSelectedTrajectory(traj.id)}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs font-medium">{traj.name}</span>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDuplicateTrajectory(traj.id);
                        }}
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteTrajectory(traj.id);
                        }}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <div
                      className="w-3 h-3 rounded"
                      style={{ backgroundColor: traj.style.color }}
                    />
                    <span>{traj.points.length} pontos</span>
                    {stats && (
                      <>
                        <span>•</span>
                        <span>{stats.duration.toFixed(1)}s</span>
                      </>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Configurações da trajetória selecionada */}
        {trajectory && (
          <div className="space-y-3 border-t pt-3">
            <Label className="text-xs font-medium">Configurações</Label>
            
            <div>
              <Label className="text-xs">Nome</Label>
              <Input
                value={trajectory.name}
                onChange={(e) => handleUpdateTrajectory(trajectory.id, { name: e.target.value })}
                className="text-xs"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs">Cor</Label>
                <div className="grid grid-cols-4 gap-1 mt-1">
                  {['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#06b6d4'].map(color => (
                    <button
                      key={color}
                      className={`w-6 h-6 rounded border-2 ${
                        trajectory.style.color === color ? 'border-primary' : 'border-muted'
                      }`}
                      style={{ backgroundColor: color }}
                      onClick={() => handleUpdateTrajectory(trajectory.id, {
                        style: { ...trajectory.style, color }
                      })}
                    />
                  ))}
                </div>
              </div>
              
              <div>
                <Label className="text-xs">Espessura</Label>
                <Slider
                  value={[trajectory.style.width]}
                  onValueChange={([width]) => handleUpdateTrajectory(trajectory.id, {
                    style: { ...trajectory.style, width }
                  })}
                  max={8}
                  min={1}
                  step={1}
                  className="mt-1"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-2">
              <div>
                <Label className="text-xs">Estilo</Label>
                <Select
                  value={trajectory.style.dashArray || 'solid'}
                  onValueChange={(value) => handleUpdateTrajectory(trajectory.id, {
                    style: { 
                      ...trajectory.style, 
                      dashArray: value === 'solid' ? '' : value 
                    }
                  })}
                >
                  <SelectTrigger className="text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="solid">Sólida</SelectItem>
                    <SelectItem value="5,5">Tracejada</SelectItem>
                    <SelectItem value="2,2">Pontilhada</SelectItem>
                    <SelectItem value="10,5,2,5">Traço-Ponto</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label className="text-xs">Tipo</Label>
                <Select
                  value={trajectory.type}
                  onValueChange={(value: any) => handleUpdateTrajectory(trajectory.id, { type: value })}
                >
                  <SelectTrigger className="text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="player">Jogador</SelectItem>
                    <SelectItem value="ball">Bola</SelectItem>
                    <SelectItem value="movement">Movimento</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Estatísticas da trajetória */}
            {(() => {
              const stats = calculateTrajectoryStats(trajectory);
              return stats ? (
                <div className="p-3 bg-muted/50 rounded-lg">
                  <Label className="text-xs font-medium mb-2 block">Estatísticas</Label>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>
                      <span className="font-medium">Pontos:</span>
                      <span className="ml-1">{stats.points}</span>
                    </div>
                    <div>
                      <span className="font-medium">Duração:</span>
                      <span className="ml-1">{stats.duration.toFixed(1)}s</span>
                    </div>
                    <div>
                      <span className="font-medium">Distância:</span>
                      <span className="ml-1">{stats.distance.toFixed(1)}m</span>
                    </div>
                    <div>
                      <span className="font-medium">Vel. Média:</span>
                      <span className="ml-1">{stats.avgSpeed.toFixed(1)}m/s</span>
                    </div>
                  </div>
                </div>
              ) : null;
            })()}
          </div>
        )}

        {/* Configurações globais */}
        <div className="space-y-2 border-t pt-3">
          <Label className="text-xs font-medium">Configurações Globais</Label>
          
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant={trajectoryStyle.showArrows ? "default" : "outline"}
              size="sm"
              onClick={() => setTrajectoryStyle(prev => ({ 
                ...prev, 
                showArrows: !prev.showArrows 
              }))}
              className="text-xs"
            >
              <ArrowRight className="h-3 w-3 mr-1" />
              Setas
            </Button>
            
            <Button
              variant={trajectoryStyle.showSpeed ? "default" : "outline"}
              size="sm"
              onClick={() => setTrajectoryStyle(prev => ({ 
                ...prev, 
                showSpeed: !prev.showSpeed 
              }))}
              className="text-xs"
            >
              <Zap className="h-3 w-3 mr-1" />
              Velocidade
            </Button>
          </div>
        </div>

        {/* Dicas */}
        <div className="p-3 bg-muted/50 rounded-lg">
          <p className="text-xs text-muted-foreground">
            💡 <strong>Dicas:</strong> Selecione um elemento e clique em "Gravar" para capturar movimentos em tempo real. 
            Use diferentes cores para distinguir tipos de movimento.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
