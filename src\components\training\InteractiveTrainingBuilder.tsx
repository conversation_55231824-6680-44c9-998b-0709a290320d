import React, { useState, useRef, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Play,
  Pause,
  Square,
  Save,
  Download,
  Upload,
  Trash2,
  Plus,
  Settings,
  Users,
  Target,
  Timer,
  Palette,
  Move,
  RotateCcw,
  ZoomIn,
  ZoomOut,
  Grid,
  Eye,
  EyeOff,
  Layers,
  Copy,
  Share2,
  BookOpen
} from 'lucide-react';
import { TrainingField } from './TrainingField';
import { ElementToolbar } from './ElementToolbar';
import { DrillSequencer } from './DrillSequencer';
import { TemplateLibrary } from './TemplateLibrary';
import { PlayerSelector } from './PlayerSelector';
import { DrawingTools } from './DrawingTools';
import { AnimationControls } from './AnimationControls';
import { TrajectorySystem } from './TrajectorySystem';
import { ExportSystem } from './ExportSystem';
import { TacticalAnalysis } from './TacticalAnalysis';
import { InteractiveTutorial } from './InteractiveTutorial';
import { AdvancedSettings } from './AdvancedSettings';
import { useToast } from '@/components/ui/use-toast';

export interface TrainingElement {
  id: string;
  type: 'cone' | 'ball' | 'goal' | 'player' | 'marker' | 'annotation';
  position: { x: number; y: number };
  properties: {
    color?: string;
    size?: 'small' | 'medium' | 'large';
    label?: string;
    playerId?: string;
    playerName?: string;
    playerNumber?: number;
    text?: string;
    rotation?: number;
  };
}

export interface DrawingElement {
  id: string;
  type: 'line' | 'arrow' | 'circle' | 'rectangle' | 'polygon' | 'text' | 'freehand';
  points: { x: number; y: number }[];
  properties: {
    strokeColor: string;
    strokeWidth: number;
    fillColor?: string;
    text?: string;
    fontSize?: number;
  };
}

export interface DrillStep {
  id: string;
  name: string;
  description: string;
  duration: number; // em segundos
  elements: TrainingElement[];
  annotations: any[];
  drawings: DrawingElement[];
}

export interface TrainingDrill {
  id: string;
  name: string;
  description: string;
  category: 'tactical' | 'technical' | 'physical' | 'transition' | 'finishing';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  steps: DrillStep[];
  totalDuration: number;
  playersRequired: number;
  equipmentNeeded: string[];
  objectives: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface InteractiveTrainingBuilderProps {
  clubId: number;
}

export function InteractiveTrainingBuilder({ clubId }: InteractiveTrainingBuilderProps) {
  const { toast } = useToast();
  const fieldRef = useRef<HTMLDivElement>(null);
  
  // Estados principais
  const [currentDrill, setCurrentDrill] = useState<TrainingDrill | null>(null);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [elements, setElements] = useState<TrainingElement[]>([]);
  const [drawings, setDrawings] = useState<DrawingElement[]>([]);
  const [selectedElements, setSelectedElements] = useState<string[]>([]);
  const [isPlaying, setIsPlaying] = useState(false);
  const [playbackSpeed, setPlaybackSpeed] = useState(1);
  const [zoom, setZoom] = useState(1);
  const [showGrid, setShowGrid] = useState(true);
  const [showPlayerNames, setShowPlayerNames] = useState(true);
  
  // Estados de ferramentas
  const [activeTab, setActiveTab] = useState('field');
  const [activeTool, setActiveTool] = useState<string | null>(null);
  const [drawingMode, setDrawingMode] = useState<'select' | 'draw' | 'erase'>('select');
  const [drawingTool, setDrawingTool] = useState<string>('line');
  const [strokeColor, setStrokeColor] = useState('#ef4444');
  const [strokeWidth, setStrokeWidth] = useState(2);
  
  // Estados de modais e dialogs
  const [showTemplateLibrary, setShowTemplateLibrary] = useState(false);
  const [showPlayerSelector, setShowPlayerSelector] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showTutorial, setShowTutorial] = useState(false);
  const [showAdvancedSettings, setShowAdvancedSettings] = useState(false);

  // Estados para animação e trajetórias
  const [currentFrame, setCurrentFrame] = useState(0);
  const [totalFrames, setTotalFrames] = useState(300);
  const [trajectories, setTrajectories] = useState<any[]>([]);
  const [isRecordingTrajectory, setIsRecordingTrajectory] = useState(false);
  const [settings, setSettings] = useState<any>({});

  // Handlers para elementos
  const handleAddElement = useCallback((element: Omit<TrainingElement, 'id'>) => {
    const newElement: TrainingElement = {
      ...element,
      id: `element_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
    setElements(prev => [...prev, newElement]);
  }, []);

  const handleUpdateElement = useCallback((id: string, updates: Partial<TrainingElement>) => {
    setElements(prev => prev.map(el => el.id === id ? { ...el, ...updates } : el));
  }, []);

  const handleDeleteElement = useCallback((id: string) => {
    setElements(prev => prev.filter(el => el.id !== id));
    setSelectedElements(prev => prev.filter(elId => elId !== id));
  }, []);

  const handleSelectElement = useCallback((id: string, multiSelect = false) => {
    if (multiSelect) {
      setSelectedElements(prev =>
        prev.includes(id) ? prev.filter(elId => elId !== id) : [...prev, id]
      );
    } else {
      setSelectedElements([id]);
    }
  }, []);

  // Handlers para desenhos
  const handleAddDrawing = useCallback((drawing: Omit<DrawingElement, 'id'>) => {
    const newDrawing: DrawingElement = {
      ...drawing,
      id: `drawing_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
    setDrawings(prev => [...prev, newDrawing]);
  }, []);

  const handleUpdateDrawing = useCallback((id: string, updates: Partial<DrawingElement>) => {
    setDrawings(prev => prev.map(drawing => drawing.id === id ? { ...drawing, ...updates } : drawing));
  }, []);

  const handleDeleteDrawing = useCallback((id: string) => {
    setDrawings(prev => prev.filter(drawing => drawing.id !== id));
  }, []);

  // Handlers para drill management
  const handleSaveDrill = useCallback(async () => {
    if (!currentDrill) {
      toast({
        title: "Erro",
        description: "Nenhum drill para salvar",
        variant: "destructive"
      });
      return;
    }

    try {
      // Aqui você implementaria a lógica de salvamento no banco de dados
      // await saveDrillToDatabase(currentDrill);
      
      toast({
        title: "Sucesso",
        description: "Drill salvo com sucesso!",
      });
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao salvar drill",
        variant: "destructive"
      });
    }
  }, [currentDrill, toast]);

  const handleExportDrill = useCallback(() => {
    if (!currentDrill) return;
    
    const dataStr = JSON.stringify(currentDrill, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${currentDrill.name.replace(/\s+/g, '_')}.json`;
    link.click();
    URL.revokeObjectURL(url);
  }, [currentDrill]);

  const handleNewDrill = useCallback(() => {
    const newDrill: TrainingDrill = {
      id: `drill_${Date.now()}`,
      name: 'Novo Drill',
      description: '',
      category: 'tactical',
      difficulty: 'beginner',
      steps: [{
        id: 'step_1',
        name: 'Configuração Inicial',
        description: '',
        duration: 300, // 5 minutos
        elements: [],
        annotations: [],
        drawings: []
      }],
      totalDuration: 300,
      playersRequired: 11,
      equipmentNeeded: [],
      objectives: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    setCurrentDrill(newDrill);
    setCurrentStepIndex(0);
    setElements([]);
    setSelectedElements([]);
  }, []);

  // Controles de zoom
  const handleZoomIn = useCallback(() => {
    setZoom(prev => Math.min(prev + 0.1, 2));
  }, []);

  const handleZoomOut = useCallback(() => {
    setZoom(prev => Math.max(prev - 0.1, 0.5));
  }, []);

  const handleResetZoom = useCallback(() => {
    setZoom(1);
  }, []);

  // Handlers para animação
  const handleCaptureFrame = useCallback(() => {
    // Capturar frame atual para animação
    toast({
      title: "Frame capturado",
      description: "Frame adicionado à sequência de animação",
    });
  }, [toast]);

  const handleExportAnimation = useCallback(() => {
    // Exportar animação como vídeo
    toast({
      title: "Exportando animação",
      description: "Gerando vídeo da sequência...",
    });
  }, [toast]);

  return (
    <div className="h-full flex flex-col space-y-4">
        {/* Header com controles principais */}
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-xl">Sistema de Micro Ciclos Interativo</CardTitle>
                <CardDescription>
                  Crie e gerencie treinos visuais com elementos arrastáveis e sequências animadas
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={handleNewDrill}>
                  <Plus className="h-4 w-4 mr-2" />
                  Novo Drill
                </Button>
                <Button variant="outline" size="sm" onClick={() => setShowTemplateLibrary(true)}>
                  <Upload className="h-4 w-4 mr-2" />
                  Templates
                </Button>
                <Button variant="outline" size="sm" onClick={() => setShowTutorial(true)}>
                  <BookOpen className="h-4 w-4 mr-2" />
                  Tutorial
                </Button>
                <Button variant="outline" size="sm" onClick={() => setShowAdvancedSettings(true)}>
                  <Settings className="h-4 w-4 mr-2" />
                  Configurações
                </Button>
                <Button variant="outline" size="sm" onClick={handleSaveDrill} disabled={!currentDrill}>
                  <Save className="h-4 w-4 mr-2" />
                  Salvar
                </Button>
                <Button variant="outline" size="sm" onClick={() => setShowExportDialog(true)} disabled={!currentDrill}>
                  <Download className="h-4 w-4 mr-2" />
                  Exportar
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Área principal com tabs */}
        <div className="flex-1 grid grid-cols-12 gap-4">
          {/* Sidebar esquerda - Ferramentas */}
          <div className="col-span-3 space-y-4">
            <ElementToolbar
              onAddElement={handleAddElement}
              activeTool={activeTool}
              onToolChange={setActiveTool}
            />
            
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Controles de Visualização</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label className="text-xs">Zoom</Label>
                  <div className="flex items-center gap-1">
                    <Button variant="outline" size="icon" className="h-6 w-6" onClick={handleZoomOut}>
                      <ZoomOut className="h-3 w-3" />
                    </Button>
                    <span className="text-xs w-12 text-center">{Math.round(zoom * 100)}%</span>
                    <Button variant="outline" size="icon" className="h-6 w-6" onClick={handleZoomIn}>
                      <ZoomIn className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <Label className="text-xs">Grade</Label>
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => setShowGrid(!showGrid)}
                  >
                    <Grid className={`h-3 w-3 ${showGrid ? 'text-primary' : 'text-muted-foreground'}`} />
                  </Button>
                </div>
                
                <div className="flex items-center justify-between">
                  <Label className="text-xs">Nomes</Label>
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => setShowPlayerNames(!showPlayerNames)}
                  >
                    {showPlayerNames ? <Eye className="h-3 w-3" /> : <EyeOff className="h-3 w-3" />}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Área central - Campo de treinamento */}
          <div className="col-span-6">
            <Card className="h-full">
              <CardContent className="p-0 h-full">
                <TrainingField
                  ref={fieldRef}
                  elements={elements}
                  drawings={drawings}
                  selectedElements={selectedElements}
                  zoom={zoom}
                  showGrid={showGrid}
                  showPlayerNames={showPlayerNames}
                  onElementUpdate={handleUpdateElement}
                  onElementSelect={handleSelectElement}
                  onElementDelete={handleDeleteElement}
                  onElementAdd={handleAddElement}
                  onDrawingAdd={handleAddDrawing}
                  drawingMode={drawingMode}
                  activeTool={activeTool}
                  drawingTool={drawingTool}
                  strokeColor={strokeColor}
                  strokeWidth={strokeWidth}
                />
              </CardContent>
            </Card>
          </div>

          {/* Sidebar direita - Propriedades e sequenciamento */}
          <div className="col-span-3 space-y-4">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-5">
                <TabsTrigger value="field" className="text-xs">Campo</TabsTrigger>
                <TabsTrigger value="sequence" className="text-xs">Sequência</TabsTrigger>
                <TabsTrigger value="players" className="text-xs">Jogadores</TabsTrigger>
                <TabsTrigger value="animation" className="text-xs">Animação</TabsTrigger>
                <TabsTrigger value="analysis" className="text-xs">Análise</TabsTrigger>
              </TabsList>
              
              <TabsContent value="field" className="space-y-4">
                <DrawingTools
                  mode={drawingMode}
                  onModeChange={setDrawingMode}
                  selectedElements={selectedElements}
                  onUpdateElements={handleUpdateElement}
                  drawingTool={drawingTool}
                  onDrawingToolChange={setDrawingTool}
                  strokeColor={strokeColor}
                  onStrokeColorChange={setStrokeColor}
                  strokeWidth={strokeWidth}
                  onStrokeWidthChange={setStrokeWidth}
                />
              </TabsContent>
              
              <TabsContent value="sequence" className="space-y-4">
                <DrillSequencer
                  drill={currentDrill}
                  currentStepIndex={currentStepIndex}
                  onStepChange={setCurrentStepIndex}
                  onDrillUpdate={setCurrentDrill}
                  isPlaying={isPlaying}
                  onPlayStateChange={setIsPlaying}
                  playbackSpeed={playbackSpeed}
                  onSpeedChange={setPlaybackSpeed}
                />
              </TabsContent>
              
              <TabsContent value="players" className="space-y-4">
                <PlayerSelector
                  clubId={clubId}
                  onPlayerSelect={(player) => {
                    handleAddElement({
                      type: 'player',
                      position: { x: 50, y: 50 },
                      properties: {
                        playerId: player.id,
                        playerName: player.name,
                        playerNumber: player.number,
                        size: 'medium'
                      }
                    });
                  }}
                />
              </TabsContent>

              <TabsContent value="animation" className="space-y-4">
                <AnimationControls
                  isPlaying={isPlaying}
                  onPlayStateChange={setIsPlaying}
                  currentFrame={currentFrame}
                  totalFrames={totalFrames}
                  onFrameChange={setCurrentFrame}
                  playbackSpeed={playbackSpeed}
                  onSpeedChange={setPlaybackSpeed}
                  onExportAnimation={handleExportAnimation}
                  onCaptureFrame={handleCaptureFrame}
                />

                <TrajectorySystem
                  trajectories={trajectories}
                  onTrajectoriesChange={setTrajectories}
                  selectedElementId={selectedElements[0]}
                  isRecording={isRecordingTrajectory}
                  onRecordingChange={setIsRecordingTrajectory}
                />
              </TabsContent>

              <TabsContent value="analysis" className="space-y-4">
                <TacticalAnalysis
                  drill={currentDrill}
                  elements={elements}
                />
              </TabsContent>
            </Tabs>
          </div>
        </div>

        {/* Modais */}
        {showTemplateLibrary && (
          <TemplateLibrary
            open={showTemplateLibrary}
            onOpenChange={setShowTemplateLibrary}
            onSelectTemplate={(template) => {
              setCurrentDrill(template);
              setCurrentStepIndex(0);
              setElements(template.steps[0]?.elements || []);
            }}
            clubId={clubId}
          />
        )}

        {showExportDialog && (
          <ExportSystem
            open={showExportDialog}
            onOpenChange={setShowExportDialog}
            drill={currentDrill}
            fieldRef={fieldRef}
          />
        )}

        {showTutorial && (
          <InteractiveTutorial
            open={showTutorial}
            onOpenChange={setShowTutorial}
            onComplete={() => {
              toast({
                title: "Tutorial concluído!",
                description: "Agora você está pronto para criar treinos incríveis!",
              });
            }}
          />
        )}

        {showAdvancedSettings && (
          <AdvancedSettings
            open={showAdvancedSettings}
            onOpenChange={setShowAdvancedSettings}
            settings={settings}
            onSettingsChange={setSettings}
          />
        )}
      </div>
  );
}
