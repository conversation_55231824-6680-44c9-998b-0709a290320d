import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Download,
  FileText,
  Image,
  Video,
  Share2,
  Printer,
  Mail,
  Cloud,
  Settings,
  Eye,
  Palette,
  Layout,
  FileImage,
  Film,
  FileDown,
  Presentation,
  BookOpen,
  Users,
  Clock,
  Target
} from 'lucide-react';
import { TrainingDrill } from './InteractiveTrainingBuilder';
import { useToast } from '@/components/ui/use-toast';

interface ExportSystemProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  drill: TrainingDrill | null;
  fieldRef?: React.RefObject<HTMLElement>;
}

interface ExportOptions {
  format: 'pdf' | 'png' | 'svg' | 'mp4' | 'gif' | 'pptx' | 'json';
  quality: 'low' | 'medium' | 'high' | 'ultra';
  includeElements: {
    field: boolean;
    elements: boolean;
    trajectories: boolean;
    annotations: boolean;
    timeline: boolean;
    instructions: boolean;
    statistics: boolean;
  };
  layout: 'portrait' | 'landscape';
  paperSize: 'A4' | 'A3' | 'Letter' | 'Custom';
  resolution: '72' | '150' | '300' | '600';
  backgroundColor: string;
  watermark: boolean;
  branding: boolean;
}

export function ExportSystem({ open, onOpenChange, drill, fieldRef }: ExportSystemProps) {
  const { toast } = useToast();
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'pdf',
    quality: 'high',
    includeElements: {
      field: true,
      elements: true,
      trajectories: true,
      annotations: true,
      timeline: false,
      instructions: true,
      statistics: true
    },
    layout: 'landscape',
    paperSize: 'A4',
    resolution: '300',
    backgroundColor: '#ffffff',
    watermark: false,
    branding: true
  });
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [previewMode, setPreviewMode] = useState(false);

  const formatOptions = [
    { value: 'pdf', label: 'PDF Document', icon: FileText, description: 'Documento completo com instruções' },
    { value: 'png', label: 'PNG Image', icon: Image, description: 'Imagem de alta qualidade' },
    { value: 'svg', label: 'SVG Vector', icon: FileImage, description: 'Gráfico vetorial escalável' },
    { value: 'mp4', label: 'MP4 Video', icon: Video, description: 'Vídeo da animação' },
    { value: 'gif', label: 'GIF Animation', icon: Film, description: 'Animação para compartilhamento' },
    { value: 'pptx', label: 'PowerPoint', icon: Presentation, description: 'Apresentação interativa' },
    { value: 'json', label: 'JSON Data', icon: FileDown, description: 'Dados estruturados do drill' }
  ];

  const handleExport = async () => {
    if (!drill) return;

    setIsExporting(true);
    setExportProgress(0);

    try {
      // Simular progresso de exportação
      const steps = [
        'Preparando dados...',
        'Renderizando campo...',
        'Processando elementos...',
        'Gerando trajetórias...',
        'Criando documento...',
        'Finalizando...'
      ];

      for (let i = 0; i < steps.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 500));
        setExportProgress(((i + 1) / steps.length) * 100);
      }

      // Aqui você implementaria a lógica real de exportação
      await exportDrill(drill, exportOptions);

      toast({
        title: "Exportação concluída",
        description: `Drill exportado como ${exportOptions.format.toUpperCase()} com sucesso!`,
      });

      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Erro na exportação",
        description: "Ocorreu um erro durante a exportação. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  const exportDrill = async (drill: TrainingDrill, options: ExportOptions) => {
    // Implementação da exportação baseada no formato
    switch (options.format) {
      case 'pdf':
        return exportToPDF(drill, options);
      case 'png':
        return exportToPNG(drill, options);
      case 'svg':
        return exportToSVG(drill, options);
      case 'mp4':
        return exportToMP4(drill, options);
      case 'gif':
        return exportToGIF(drill, options);
      case 'pptx':
        return exportToPPTX(drill, options);
      case 'json':
        return exportToJSON(drill, options);
      default:
        throw new Error('Formato não suportado');
    }
  };

  const exportToPDF = async (drill: TrainingDrill, options: ExportOptions) => {
    // Implementação específica para PDF
    const content = generatePDFContent(drill, options);
    const blob = new Blob([content], { type: 'application/pdf' });
    downloadFile(blob, `${drill.name}.pdf`);
  };

  const exportToPNG = async (drill: TrainingDrill, options: ExportOptions) => {
    // Capturar o canvas do campo
    if (fieldRef?.current) {
      const canvas = await html2canvas(fieldRef.current);
      canvas.toBlob((blob) => {
        if (blob) downloadFile(blob, `${drill.name}.png`);
      });
    }
  };

  const exportToJSON = async (drill: TrainingDrill, options: ExportOptions) => {
    const data = JSON.stringify(drill, null, 2);
    const blob = new Blob([data], { type: 'application/json' });
    downloadFile(blob, `${drill.name}.json`);
  };

  const generatePDFContent = (drill: TrainingDrill, options: ExportOptions) => {
    // Gerar conteúdo estruturado para PDF
    return `
      Drill: ${drill.name}
      Categoria: ${drill.category}
      Dificuldade: ${drill.difficulty}
      Duração: ${drill.totalDuration}s
      Jogadores: ${drill.playersRequired}
      
      Objetivos:
      ${drill.objectives.map(obj => `- ${obj}`).join('\n')}
      
      Passos:
      ${drill.steps.map((step, index) => `
        ${index + 1}. ${step.name} (${step.duration}s)
        ${step.description}
      `).join('\n')}
    `;
  };

  const downloadFile = (blob: Blob, filename: string) => {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.click();
    URL.revokeObjectURL(url);
  };

  const updateExportOption = (key: keyof ExportOptions, value: any) => {
    setExportOptions(prev => ({ ...prev, [key]: value }));
  };

  const updateIncludeElement = (key: keyof ExportOptions['includeElements'], value: boolean) => {
    setExportOptions(prev => ({
      ...prev,
      includeElements: { ...prev.includeElements, [key]: value }
    }));
  };

  if (!drill) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Exportar Drill: {drill.name}
          </DialogTitle>
          <DialogDescription>
            Configure as opções de exportação e escolha o formato desejado
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="format" className="flex-1">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="format">Formato</TabsTrigger>
            <TabsTrigger value="content">Conteúdo</TabsTrigger>
            <TabsTrigger value="layout">Layout</TabsTrigger>
            <TabsTrigger value="advanced">Avançado</TabsTrigger>
          </TabsList>

          <TabsContent value="format" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {formatOptions.map(format => {
                const Icon = format.icon;
                return (
                  <Card
                    key={format.value}
                    className={`cursor-pointer transition-all ${
                      exportOptions.format === format.value
                        ? 'border-primary bg-primary/5'
                        : 'hover:border-primary/50'
                    }`}
                    onClick={() => updateExportOption('format', format.value)}
                  >
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center gap-2">
                        <Icon className="h-4 w-4" />
                        {format.label}
                      </CardTitle>
                      <CardDescription className="text-xs">
                        {format.description}
                      </CardDescription>
                    </CardHeader>
                  </Card>
                );
              })}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm">Qualidade</Label>
                <Select
                  value={exportOptions.quality}
                  onValueChange={(value: any) => updateExportOption('quality', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Baixa (Rápido)</SelectItem>
                    <SelectItem value="medium">Média</SelectItem>
                    <SelectItem value="high">Alta</SelectItem>
                    <SelectItem value="ultra">Ultra (Lento)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-sm">Resolução (DPI)</Label>
                <Select
                  value={exportOptions.resolution}
                  onValueChange={(value) => updateExportOption('resolution', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="72">72 DPI (Web)</SelectItem>
                    <SelectItem value="150">150 DPI (Padrão)</SelectItem>
                    <SelectItem value="300">300 DPI (Impressão)</SelectItem>
                    <SelectItem value="600">600 DPI (Alta qualidade)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="content" className="space-y-4">
            <div className="space-y-3">
              <Label className="text-sm font-medium">Elementos a incluir:</Label>
              
              {Object.entries(exportOptions.includeElements).map(([key, value]) => (
                <div key={key} className="flex items-center space-x-2">
                  <Checkbox
                    id={key}
                    checked={value}
                    onCheckedChange={(checked) => 
                      updateIncludeElement(key as any, checked as boolean)
                    }
                  />
                  <Label htmlFor={key} className="text-sm capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </Label>
                </div>
              ))}
            </div>

            <div className="space-y-2">
              <Label className="text-sm">Notas adicionais</Label>
              <Textarea
                placeholder="Adicione instruções ou observações que serão incluídas na exportação..."
                className="min-h-20"
              />
            </div>
          </TabsContent>

          <TabsContent value="layout" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-sm">Orientação</Label>
                <Select
                  value={exportOptions.layout}
                  onValueChange={(value: any) => updateExportOption('layout', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="portrait">Retrato</SelectItem>
                    <SelectItem value="landscape">Paisagem</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className="text-sm">Tamanho do papel</Label>
                <Select
                  value={exportOptions.paperSize}
                  onValueChange={(value: any) => updateExportOption('paperSize', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="A4">A4</SelectItem>
                    <SelectItem value="A3">A3</SelectItem>
                    <SelectItem value="Letter">Letter</SelectItem>
                    <SelectItem value="Custom">Personalizado</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label className="text-sm">Cor de fundo</Label>
              <div className="grid grid-cols-6 gap-2 mt-2">
                {['#ffffff', '#f8f9fa', '#e9ecef', '#dee2e6', '#000000', '#1a1a1a'].map(color => (
                  <button
                    key={color}
                    className={`w-8 h-8 rounded border-2 ${
                      exportOptions.backgroundColor === color ? 'border-primary' : 'border-muted'
                    }`}
                    style={{ backgroundColor: color }}
                    onClick={() => updateExportOption('backgroundColor', color)}
                  />
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="watermark"
                  checked={exportOptions.watermark}
                  onCheckedChange={(checked) => updateExportOption('watermark', checked)}
                />
                <Label htmlFor="watermark" className="text-sm">
                  Incluir marca d'água
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="branding"
                  checked={exportOptions.branding}
                  onCheckedChange={(checked) => updateExportOption('branding', checked)}
                />
                <Label htmlFor="branding" className="text-sm">
                  Incluir logo do clube
                </Label>
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-sm">Opções de compartilhamento</Label>
              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline" size="sm">
                  <Mail className="h-4 w-4 mr-2" />
                  Enviar por email
                </Button>
                <Button variant="outline" size="sm">
                  <Cloud className="h-4 w-4 mr-2" />
                  Salvar na nuvem
                </Button>
                <Button variant="outline" size="sm">
                  <Share2 className="h-4 w-4 mr-2" />
                  Compartilhar link
                </Button>
                <Button variant="outline" size="sm">
                  <Printer className="h-4 w-4 mr-2" />
                  Imprimir
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Preview e estatísticas */}
        <div className="border-t pt-4">
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span>Duração: {drill.totalDuration}s</span>
            </div>
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span>Jogadores: {drill.playersRequired}</span>
            </div>
            <div className="flex items-center gap-2">
              <Target className="h-4 w-4 text-muted-foreground" />
              <span>Passos: {drill.steps.length}</span>
            </div>
          </div>
        </div>

        {/* Progresso de exportação */}
        {isExporting && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Exportando...</span>
              <span>{Math.round(exportProgress)}%</span>
            </div>
            <Progress value={exportProgress} />
          </div>
        )}

        {/* Ações */}
        <div className="flex justify-between items-center pt-4 border-t">
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={() => setPreviewMode(!previewMode)}>
              <Eye className="h-4 w-4 mr-2" />
              {previewMode ? 'Ocultar' : 'Visualizar'} Preview
            </Button>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancelar
            </Button>
            <Button onClick={handleExport} disabled={isExporting}>
              {isExporting ? (
                <>Exportando...</>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Exportar {exportOptions.format.toUpperCase()}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
