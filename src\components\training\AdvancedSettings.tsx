import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Settings,
  Palette,
  Grid,
  Ruler,
  Eye,
  Zap,
  Save,
  RotateCcw,
  Monitor,
  Keyboard,
  Mouse,
  Gamepad2,
  Volume2,
  Bell,
  Shield,
  Database,
  Cloud,
  Download,
  Upload
} from 'lucide-react';

interface AdvancedSettingsProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  settings: any;
  onSettingsChange: (settings: any) => void;
}

export function AdvancedSettings({ 
  open, 
  onOpenChange, 
  settings, 
  onSettingsChange 
}: AdvancedSettingsProps) {
  const [localSettings, setLocalSettings] = useState(settings);

  const handleSave = () => {
    onSettingsChange(localSettings);
    onOpenChange(false);
  };

  const handleReset = () => {
    const defaultSettings = {
      field: {
        showGrid: true,
        gridSize: 5,
        fieldColor: '#10b981',
        backgroundColor: '#f0fdf4',
        showCoordinates: false,
        showMeasurements: false,
        fieldScale: 1,
        showZones: true
      },
      ui: {
        theme: 'light',
        language: 'pt-BR',
        animations: true,
        tooltips: true,
        confirmActions: true,
        autoSave: true,
        autoSaveInterval: 30,
        soundEffects: false,
        notifications: true
      },
      performance: {
        renderQuality: 'high',
        maxElements: 100,
        animationFPS: 60,
        enableGPU: true,
        cacheElements: true,
        optimizeRendering: true
      },
      controls: {
        mouseWheelZoom: true,
        keyboardShortcuts: true,
        touchGestures: true,
        snapToGrid: true,
        snapDistance: 10,
        multiSelect: true,
        dragSensitivity: 1
      },
      export: {
        defaultFormat: 'pdf',
        defaultQuality: 'high',
        includeWatermark: false,
        includeBranding: true,
        autoBackup: true,
        cloudSync: false
      }
    };
    setLocalSettings(defaultSettings);
  };

  const updateSetting = (category: string, key: string, value: any) => {
    setLocalSettings((prev: any) => ({
      ...prev,
      [category]: {
        ...prev[category],
        [key]: value
      }
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Configurações Avançadas
          </DialogTitle>
          <DialogDescription>
            Personalize a experiência do sistema de micro ciclos
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="field" className="flex-1">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="field">Campo</TabsTrigger>
            <TabsTrigger value="ui">Interface</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="controls">Controles</TabsTrigger>
            <TabsTrigger value="export">Exportação</TabsTrigger>
          </TabsList>

          <TabsContent value="field" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Grid className="h-4 w-4" />
                  Aparência do Campo
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Mostrar grade</Label>
                    <Switch
                      checked={localSettings.field?.showGrid}
                      onCheckedChange={(checked) => updateSetting('field', 'showGrid', checked)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Tamanho da grade</Label>
                    <Slider
                      value={[localSettings.field?.gridSize || 5]}
                      onValueChange={([value]) => updateSetting('field', 'gridSize', value)}
                      max={20}
                      min={1}
                      step={1}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Cor do campo</Label>
                    <div className="grid grid-cols-6 gap-2">
                      {['#10b981', '#22c55e', '#16a34a', '#15803d', '#166534', '#14532d'].map(color => (
                        <button
                          key={color}
                          className={`w-8 h-8 rounded border-2 ${
                            localSettings.field?.fieldColor === color ? 'border-primary' : 'border-muted'
                          }`}
                          style={{ backgroundColor: color }}
                          onClick={() => updateSetting('field', 'fieldColor', color)}
                        />
                      ))}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Cor de fundo</Label>
                    <div className="grid grid-cols-6 gap-2">
                      {['#f0fdf4', '#ffffff', '#f8fafc', '#f1f5f9', '#e2e8f0', '#cbd5e1'].map(color => (
                        <button
                          key={color}
                          className={`w-8 h-8 rounded border-2 ${
                            localSettings.field?.backgroundColor === color ? 'border-primary' : 'border-muted'
                          }`}
                          style={{ backgroundColor: color }}
                          onClick={() => updateSetting('field', 'backgroundColor', color)}
                        />
                      ))}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Mostrar coordenadas</Label>
                    <Switch
                      checked={localSettings.field?.showCoordinates}
                      onCheckedChange={(checked) => updateSetting('field', 'showCoordinates', checked)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Mostrar medidas</Label>
                    <Switch
                      checked={localSettings.field?.showMeasurements}
                      onCheckedChange={(checked) => updateSetting('field', 'showMeasurements', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="ui" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Monitor className="h-4 w-4" />
                  Interface do Usuário
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Tema</Label>
                    <Select
                      value={localSettings.ui?.theme}
                      onValueChange={(value) => updateSetting('ui', 'theme', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="light">Claro</SelectItem>
                        <SelectItem value="dark">Escuro</SelectItem>
                        <SelectItem value="auto">Automático</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Idioma</Label>
                    <Select
                      value={localSettings.ui?.language}
                      onValueChange={(value) => updateSetting('ui', 'language', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pt-BR">Português (BR)</SelectItem>
                        <SelectItem value="en-US">English (US)</SelectItem>
                        <SelectItem value="es-ES">Español</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Animações</Label>
                    <Switch
                      checked={localSettings.ui?.animations}
                      onCheckedChange={(checked) => updateSetting('ui', 'animations', checked)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Tooltips</Label>
                    <Switch
                      checked={localSettings.ui?.tooltips}
                      onCheckedChange={(checked) => updateSetting('ui', 'tooltips', checked)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Confirmar ações</Label>
                    <Switch
                      checked={localSettings.ui?.confirmActions}
                      onCheckedChange={(checked) => updateSetting('ui', 'confirmActions', checked)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Salvamento automático</Label>
                    <Switch
                      checked={localSettings.ui?.autoSave}
                      onCheckedChange={(checked) => updateSetting('ui', 'autoSave', checked)}
                    />
                  </div>
                </div>

                {localSettings.ui?.autoSave && (
                  <div className="space-y-2">
                    <Label>Intervalo de salvamento (segundos)</Label>
                    <Slider
                      value={[localSettings.ui?.autoSaveInterval || 30]}
                      onValueChange={([value]) => updateSetting('ui', 'autoSaveInterval', value)}
                      max={300}
                      min={10}
                      step={10}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Performance e Otimização
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Qualidade de renderização</Label>
                    <Select
                      value={localSettings.performance?.renderQuality}
                      onValueChange={(value) => updateSetting('performance', 'renderQuality', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Baixa</SelectItem>
                        <SelectItem value="medium">Média</SelectItem>
                        <SelectItem value="high">Alta</SelectItem>
                        <SelectItem value="ultra">Ultra</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>FPS de animação</Label>
                    <Select
                      value={localSettings.performance?.animationFPS?.toString()}
                      onValueChange={(value) => updateSetting('performance', 'animationFPS', parseInt(value))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="30">30 FPS</SelectItem>
                        <SelectItem value="60">60 FPS</SelectItem>
                        <SelectItem value="120">120 FPS</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Aceleração GPU</Label>
                    <Switch
                      checked={localSettings.performance?.enableGPU}
                      onCheckedChange={(checked) => updateSetting('performance', 'enableGPU', checked)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Cache de elementos</Label>
                    <Switch
                      checked={localSettings.performance?.cacheElements}
                      onCheckedChange={(checked) => updateSetting('performance', 'cacheElements', checked)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Máximo de elementos ({localSettings.performance?.maxElements || 100})</Label>
                  <Slider
                    value={[localSettings.performance?.maxElements || 100]}
                    onValueChange={([value]) => updateSetting('performance', 'maxElements', value)}
                    max={500}
                    min={50}
                    step={10}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="controls" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Mouse className="h-4 w-4" />
                  Controles e Interação
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Zoom com scroll</Label>
                    <Switch
                      checked={localSettings.controls?.mouseWheelZoom}
                      onCheckedChange={(checked) => updateSetting('controls', 'mouseWheelZoom', checked)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Atalhos de teclado</Label>
                    <Switch
                      checked={localSettings.controls?.keyboardShortcuts}
                      onCheckedChange={(checked) => updateSetting('controls', 'keyboardShortcuts', checked)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Snap to grid</Label>
                    <Switch
                      checked={localSettings.controls?.snapToGrid}
                      onCheckedChange={(checked) => updateSetting('controls', 'snapToGrid', checked)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Seleção múltipla</Label>
                    <Switch
                      checked={localSettings.controls?.multiSelect}
                      onCheckedChange={(checked) => updateSetting('controls', 'multiSelect', checked)}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Sensibilidade do drag ({localSettings.controls?.dragSensitivity || 1})</Label>
                  <Slider
                    value={[localSettings.controls?.dragSensitivity || 1]}
                    onValueChange={([value]) => updateSetting('controls', 'dragSensitivity', value)}
                    max={3}
                    min={0.1}
                    step={0.1}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="export" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-sm flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  Exportação e Backup
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Formato padrão</Label>
                    <Select
                      value={localSettings.export?.defaultFormat}
                      onValueChange={(value) => updateSetting('export', 'defaultFormat', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pdf">PDF</SelectItem>
                        <SelectItem value="png">PNG</SelectItem>
                        <SelectItem value="svg">SVG</SelectItem>
                        <SelectItem value="json">JSON</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Qualidade padrão</Label>
                    <Select
                      value={localSettings.export?.defaultQuality}
                      onValueChange={(value) => updateSetting('export', 'defaultQuality', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Baixa</SelectItem>
                        <SelectItem value="medium">Média</SelectItem>
                        <SelectItem value="high">Alta</SelectItem>
                        <SelectItem value="ultra">Ultra</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Backup automático</Label>
                    <Switch
                      checked={localSettings.export?.autoBackup}
                      onCheckedChange={(checked) => updateSetting('export', 'autoBackup', checked)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label>Sincronização na nuvem</Label>
                    <Switch
                      checked={localSettings.export?.cloudSync}
                      onCheckedChange={(checked) => updateSetting('export', 'cloudSync', checked)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-between items-center pt-4 border-t">
          <Button variant="outline" onClick={handleReset}>
            <RotateCcw className="h-4 w-4 mr-2" />
            Restaurar Padrões
          </Button>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancelar
            </Button>
            <Button onClick={handleSave}>
              <Save className="h-4 w-4 mr-2" />
              Salvar Configurações
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
