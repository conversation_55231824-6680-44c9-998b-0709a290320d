import { useState, useCallback, useRef, useEffect } from 'react';
import { TrainingDrill, TrainingElement, DrillStep } from '@/components/training/InteractiveTrainingBuilder';

interface UseTrainingBuilderProps {
  clubId: number;
  initialDrill?: TrainingDrill;
}

interface TrainingBuilderState {
  currentDrill: TrainingDrill | null;
  currentStepIndex: number;
  elements: TrainingElement[];
  selectedElements: string[];
  isPlaying: boolean;
  playbackSpeed: number;
  zoom: number;
  showGrid: boolean;
  showPlayerNames: boolean;
  activeTool: string | null;
  drawingMode: 'select' | 'draw' | 'erase';
  trajectories: any[];
  isRecordingTrajectory: boolean;
  currentFrame: number;
  totalFrames: number;
  settings: any;
}

export function useTrainingBuilder({ clubId, initialDrill }: UseTrainingBuilderProps) {
  const fieldRef = useRef<HTMLDivElement>(null);
  
  const [state, setState] = useState<TrainingBuilderState>({
    currentDrill: initialDrill || null,
    currentStepIndex: 0,
    elements: [],
    selectedElements: [],
    isPlaying: false,
    playbackSpeed: 1,
    zoom: 1,
    showGrid: true,
    showPlayerNames: true,
    activeTool: null,
    drawingMode: 'select',
    trajectories: [],
    isRecordingTrajectory: false,
    currentFrame: 0,
    totalFrames: 300,
    settings: {
      field: {
        showGrid: true,
        gridSize: 5,
        fieldColor: '#10b981',
        backgroundColor: '#f0fdf4',
        showCoordinates: false,
        showMeasurements: false,
        fieldScale: 1,
        showZones: true
      },
      ui: {
        theme: 'light',
        language: 'pt-BR',
        animations: true,
        tooltips: true,
        confirmActions: true,
        autoSave: true,
        autoSaveInterval: 30,
        soundEffects: false,
        notifications: true
      },
      performance: {
        renderQuality: 'high',
        maxElements: 100,
        animationFPS: 60,
        enableGPU: true,
        cacheElements: true,
        optimizeRendering: true
      },
      controls: {
        mouseWheelZoom: true,
        keyboardShortcuts: true,
        touchGestures: true,
        snapToGrid: true,
        snapDistance: 10,
        multiSelect: true,
        dragSensitivity: 1
      },
      export: {
        defaultFormat: 'pdf',
        defaultQuality: 'high',
        includeWatermark: false,
        includeBranding: true,
        autoBackup: true,
        cloudSync: false
      }
    }
  });

  // Auto-save functionality
  useEffect(() => {
    if (!state.settings.ui.autoSave || !state.currentDrill) return;

    const interval = setInterval(() => {
      saveDrillToLocalStorage(state.currentDrill);
    }, state.settings.ui.autoSaveInterval * 1000);

    return () => clearInterval(interval);
  }, [state.currentDrill, state.settings.ui.autoSave, state.settings.ui.autoSaveInterval]);

  // Keyboard shortcuts
  useEffect(() => {
    if (!state.settings.controls.keyboardShortcuts) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Prevent default browser shortcuts when focused on the field
      if (e.target === fieldRef.current || fieldRef.current?.contains(e.target as Node)) {
        switch (e.key) {
          case ' ':
            e.preventDefault();
            setState(prev => ({ ...prev, isPlaying: !prev.isPlaying }));
            break;
          case 'Delete':
          case 'Backspace':
            e.preventDefault();
            deleteSelectedElements();
            break;
          case 'Escape':
            e.preventDefault();
            setState(prev => ({ ...prev, selectedElements: [] }));
            break;
          case 'ArrowLeft':
            e.preventDefault();
            setState(prev => ({ 
              ...prev, 
              currentFrame: Math.max(0, prev.currentFrame - 1) 
            }));
            break;
          case 'ArrowRight':
            e.preventDefault();
            setState(prev => ({ 
              ...prev, 
              currentFrame: Math.min(prev.totalFrames, prev.currentFrame + 1) 
            }));
            break;
        }

        // Ctrl/Cmd shortcuts
        if (e.ctrlKey || e.metaKey) {
          switch (e.key) {
            case 'a':
              e.preventDefault();
              selectAllElements();
              break;
            case 'd':
              e.preventDefault();
              duplicateSelectedElements();
              break;
            case 's':
              e.preventDefault();
              saveDrill();
              break;
            case 'z':
              e.preventDefault();
              if (e.shiftKey) {
                redo();
              } else {
                undo();
              }
              break;
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [state.settings.controls.keyboardShortcuts]);

  // Actions
  const updateState = useCallback((updates: Partial<TrainingBuilderState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const handleAddElement = useCallback((element: Omit<TrainingElement, 'id'>) => {
    const newElement: TrainingElement = {
      ...element,
      id: `element_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
    setState(prev => ({ ...prev, elements: [...prev.elements, newElement] }));
  }, []);

  const handleUpdateElement = useCallback((id: string, updates: Partial<TrainingElement>) => {
    setState(prev => ({
      ...prev,
      elements: prev.elements.map(el => el.id === id ? { ...el, ...updates } : el)
    }));
  }, []);

  const handleDeleteElement = useCallback((id: string) => {
    setState(prev => ({
      ...prev,
      elements: prev.elements.filter(el => el.id !== id),
      selectedElements: prev.selectedElements.filter(elId => elId !== id)
    }));
  }, []);

  const handleSelectElement = useCallback((id: string, multiSelect = false) => {
    setState(prev => {
      if (multiSelect) {
        return {
          ...prev,
          selectedElements: prev.selectedElements.includes(id) 
            ? prev.selectedElements.filter(elId => elId !== id) 
            : [...prev.selectedElements, id]
        };
      } else {
        return { ...prev, selectedElements: [id] };
      }
    });
  }, []);

  const deleteSelectedElements = useCallback(() => {
    setState(prev => ({
      ...prev,
      elements: prev.elements.filter(el => !prev.selectedElements.includes(el.id)),
      selectedElements: []
    }));
  }, []);

  const selectAllElements = useCallback(() => {
    setState(prev => ({
      ...prev,
      selectedElements: prev.elements.map(el => el.id)
    }));
  }, []);

  const duplicateSelectedElements = useCallback(() => {
    setState(prev => {
      const selectedEls = prev.elements.filter(el => prev.selectedElements.includes(el.id));
      const duplicated = selectedEls.map(el => ({
        ...el,
        id: `element_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        position: { x: el.position.x + 5, y: el.position.y + 5 }
      }));
      
      return {
        ...prev,
        elements: [...prev.elements, ...duplicated],
        selectedElements: duplicated.map(el => el.id)
      };
    });
  }, []);

  const saveDrill = useCallback(async () => {
    if (!state.currentDrill) return;
    
    try {
      // Implement actual save logic here
      await saveDrillToDatabase(state.currentDrill);
      saveDrillToLocalStorage(state.currentDrill);
    } catch (error) {
      console.error('Error saving drill:', error);
    }
  }, [state.currentDrill]);

  const saveDrillToLocalStorage = useCallback((drill: TrainingDrill) => {
    try {
      localStorage.setItem(`drill_${drill.id}`, JSON.stringify(drill));
      localStorage.setItem('lastSavedDrill', drill.id);
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  }, []);

  const saveDrillToDatabase = useCallback(async (drill: TrainingDrill) => {
    // Implement database save logic
    console.log('Saving drill to database:', drill);
  }, []);

  const undo = useCallback(() => {
    // Implement undo logic
    console.log('Undo action');
  }, []);

  const redo = useCallback(() => {
    // Implement redo logic
    console.log('Redo action');
  }, []);

  const createNewDrill = useCallback(() => {
    const newDrill: TrainingDrill = {
      id: `drill_${Date.now()}`,
      name: 'Novo Drill',
      description: '',
      category: 'tactical',
      difficulty: 'beginner',
      steps: [{
        id: 'step_1',
        name: 'Configuração Inicial',
        description: '',
        duration: 300,
        elements: [],
        annotations: [],
        drawings: []
      }],
      totalDuration: 300,
      playersRequired: 11,
      equipmentNeeded: [],
      objectives: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    setState(prev => ({
      ...prev,
      currentDrill: newDrill,
      currentStepIndex: 0,
      elements: [],
      selectedElements: []
    }));
  }, []);

  const loadDrill = useCallback((drill: TrainingDrill) => {
    setState(prev => ({
      ...prev,
      currentDrill: drill,
      currentStepIndex: 0,
      elements: drill.steps[0]?.elements || [],
      selectedElements: []
    }));
  }, []);

  return {
    // State
    ...state,
    fieldRef,
    
    // Actions
    updateState,
    handleAddElement,
    handleUpdateElement,
    handleDeleteElement,
    handleSelectElement,
    deleteSelectedElements,
    selectAllElements,
    duplicateSelectedElements,
    saveDrill,
    createNewDrill,
    loadDrill,
    undo,
    redo,
    
    // Setters for individual state properties
    setCurrentDrill: (drill: TrainingDrill | null) => updateState({ currentDrill: drill }),
    setCurrentStepIndex: (index: number) => updateState({ currentStepIndex: index }),
    setElements: (elements: TrainingElement[]) => updateState({ elements }),
    setSelectedElements: (elements: string[]) => updateState({ selectedElements: elements }),
    setIsPlaying: (playing: boolean) => updateState({ isPlaying: playing }),
    setPlaybackSpeed: (speed: number) => updateState({ playbackSpeed: speed }),
    setZoom: (zoom: number) => updateState({ zoom }),
    setShowGrid: (show: boolean) => updateState({ showGrid: show }),
    setShowPlayerNames: (show: boolean) => updateState({ showPlayerNames: show }),
    setActiveTool: (tool: string | null) => updateState({ activeTool: tool }),
    setDrawingMode: (mode: 'select' | 'draw' | 'erase') => updateState({ drawingMode: mode }),
    setTrajectories: (trajectories: any[]) => updateState({ trajectories }),
    setIsRecordingTrajectory: (recording: boolean) => updateState({ isRecordingTrajectory: recording }),
    setCurrentFrame: (frame: number) => updateState({ currentFrame: frame }),
    setTotalFrames: (frames: number) => updateState({ totalFrames: frames }),
    setSettings: (settings: any) => updateState({ settings })
  };
}
